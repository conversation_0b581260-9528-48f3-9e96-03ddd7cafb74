#show table.cell.where(y: 0): strong
#show table.cell.where(x: 0): strong
#show table.cell: set align(center + horizon)

#set text(font: "LXGW WenKai Mono", lang: "zh", size: 11pt)
#table(
  columns: 4,
  [分段],table.cell(colspan: 3, [机制]),
  [青铜], table.cell(rowspan: 5, [有50%几率
#linebreak()
#text("你的对手
胜场比你少1场", fill: red)
#linebreak()
#linebreak()
#text("你的命中率和暴击率
（仅限一次）减少20%", fill: blue)
#linebreak()
#text("对手命中率和暴击率
（仅限一次）增加20%", fill: blue)]),table.cell(rowspan: 4, [转职前净负2场时
 #linebreak()
 或只剩1血时
  #linebreak()
  #text("你的对手随机掉落
一件非武器的物品", fill: red)
 #linebreak()
#linebreak()
转职前净负至少3场时
#linebreak()
#text("你的对手
降级为其前一回合的物品", fill: red)
 #linebreak()
#linebreak()
你的物品冷却
#linebreak()
在0.95-1.05倍之间波动
#linebreak()
#text("你的对手冷却
在0.975-1.05倍之间波动", fill: red)]),table.cell(rowspan: 2, [白银43或以下，且
#linebreak()
第一次游玩时 #linebreak()（每回合）
#linebreak()
第二次游玩时 #linebreak()（每4回合3次）
#linebreak()
第三次游玩且0胜时 #linebreak()（每4回合2次）
#linebreak()
第四次游玩且1胜时 #linebreak()（每4回合1次）
#linebreak()
#text("你的对手
降级为其前一回合的物品", fill: red)]),
  [白银], 
  [黄金], table.cell(stroke: none, rowspan: 2)[如果无法降到前面回合 #linebreak() 就改为掉落物品],
  [白金], 
  [钻石], table.cell(stroke: none)[], table.cell(stroke: none)[],
  [大师及以上], table.cell(colspan: 3, text("加时赛打完18场所获得的额外奖励分数减半
（胜负所获得的分数不受影响）", fill: blue)),
)