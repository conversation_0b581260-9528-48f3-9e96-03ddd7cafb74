#import "tablex.typ": tablex, colspanx, rowspanx
#set page(
  "a3",
  flipped: true,
)

#set text(font: "LXGW WenKai Mono", lang: "zh", size: 16pt)

#tablex(
  columns: 12,
  align: horizon + center,
  rowspanx(2)[*卡包*], rowspanx(2)[*细分*], colspanx(2)[*普通*], (), colspanx(4)[*金卡*], (), (), (), colspanx(4)[*异画*], (), (), (), 
  (), (), [紫卡], [橙卡], [白卡], [蓝卡], [紫卡], [橙卡], [白卡], [蓝卡], [紫卡], [橙卡],
  [*异画金色包*], [巫妖王的进军], rowspanx(4)[×], rowspanx(4)[×], rowspanx(4)[], rowspanx(4)[], rowspanx(4)[每10包], rowspanx(2)[前10包 \ 每40包], rowspanx(3)[], rowspanx(3)[], rowspanx(3)[], [每14包],
  rowspanx(3)[*金色卡包*], [版本包\ 巫妖王后], (), (), (), (), (), ()
  , (), (), (), [每40包],
  (), [版本包\ 标准狂野等], (), (), (), (), (), [每40包], (), (), (), [每361包],
  (), [版本包\ 贫瘠之地后\ 巫妖王前], (), (), (), (), (), [可能前10包\ 每40包], [不存在], [不存在], [不存在], [不存在],
  rowspanx(5)[*普通卡包*], [版本包\ 巫妖王后], rowspanx(5)[每10包], [前10包\ 每40包], rowspanx(5)[每25包], rowspanx(5)[每29包], rowspanx(5)[每142包], rowspanx(5)[每361包], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每361包],
  (), [非版本包\ 标准包职业包], (), rowspanx(2)[可能前10包\ 每40包], (), (), (), (), (), (), (), (),
  (), [非版本包\ 狂野包], (), (), (), (), (), (), [×], [×], [×], [×],
  (), [版本包\ 冰封王座后\ 巫妖王前], (), [前10包 \ 每40包], (), (), (), (), rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在],
  (), [版本包\ 冰封王座前], (), [每40包], (), (), (), (), (), (), (), (),
)

/*
#tablex(
  columns: 12,
  align: horizon + center,
  rowspanx(2)[*卡包*], rowspanx(2)[*细分*], colspanx(2)[*普通*], (), colspanx(4)[*金卡*], (), (), (), colspanx(4)[*异画*], (), (), (), 
  (), (), [紫卡], [橙卡], [白卡], [蓝卡], [紫卡], [橙卡], [白卡], [蓝卡], [紫卡], [橙卡],
  rowspanx(5)[*金色卡包*], rowspanx(2)[版本包\ 巫妖王以后], rowspanx(2)[×], rowspanx(2)[×], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每10包], [前10包], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每40包],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  (), [非版本包\ 标准狂野等], [×], [×], [], [], [每10包], [每40包], [], [], [], [每361包],
  (), rowspanx(2)[版本包\贫瘠之地后\巫妖王前], rowspanx(2)[×], rowspanx(2)[×], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每10包], [可能前10包], rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  rowspanx(2)[*异画金色包*], rowspanx(2)[巫妖王的进军], rowspanx(2)[×], rowspanx(2)[×], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每10包], [前10包], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每14包],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  rowspanx(9)[*普通卡包*], rowspanx(2)[版本包\ 巫妖王后], rowspanx(2)[每10包], [前10包], rowspanx(2)[每25包], rowspanx(2)[每29包], rowspanx(2)[每142包], rowspanx(2)[每361包], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每361包],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  (), rowspanx(2)[非版本包\ 标准包职业包], rowspanx(2)[每10包], [可能前10包], rowspanx(2)[每25包], rowspanx(2)[每29包], rowspanx(2)[每142包], rowspanx(2)[每361包], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[], rowspanx(2)[每361包],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  (), rowspanx(2)[非版本包\ 狂野包], rowspanx(2)[每10包], [可能前10包], rowspanx(2)[每25包], rowspanx(2)[每29包], rowspanx(2)[每142包], rowspanx(2)[每361包], rowspanx(2)[×], rowspanx(2)[×], rowspanx(2)[×], rowspanx(2)[×],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  (), rowspanx(2)[版本包\ 冰封王座后\ 巫妖王前], rowspanx(2)[每10包], [前10包], rowspanx(2)[每25包], rowspanx(2)[每29包], rowspanx(2)[每142包], rowspanx(2)[每361包], rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在], rowspanx(2)[不存在],
  (), (), (), (), (), (), (), [每40包], (), (), (), (), 
  (), [版本包\ 冰封王座前], [每10包], [每40包], [每25包], [每29包], [每142包], [每361包], [不存在], [不存在], [不存在], [不存在],
)
*/

