#import "titlepagetoc.typ": *

// Take a look at the file `template.typ` in the file panel
// to customize this template and discover how it works.
#show: project.with(
  title: "TitlePageTOC",
  authors: (
    (name: "<PERSON><PERSON>", email: "<EMAIL>"),
  ),
  date: "March 28, 2023",
)

// We generated the example code below so you can see how
// your document will look. Go ahead and replace it with
// your own content!

= Introaaaaaaaaaaaduction
#lorem(60)
Glaciers as the one shown in
@glaciers will cease to exist if
we don't take action soon!

#figure(
  image("test/glacier.png", width: 80%),
  caption: [
    _Glaciers_ form an important part
    of the earth's climate system.
  ],
) <glaciers>

== In this paper
#lorem(20)

=== Contributions
#lorem(40)

= Related Work
#lorem(500)
