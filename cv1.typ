#let configuration = toml("cv_configuration.toml")

#show link: set text(blue)

#show heading: h => [
  #set text(
    size: eval(configuration.font.size.heading_large),
    font: configuration.font.general,
  )
  #h
]

#let sidebarSection = {
  [
    #par(justify: true)[

      #par[
        #set text(
          size: eval(configuration.font.size.contacts),
          font: configuration.font.minor_highlight,
        )

        Email: #link("mailto:" + configuration.contacts.email) \
        Phone: #link("tel:" + configuration.contacts.phone) \
        LinkedIn: #link(configuration.contacts.linkedin.url)[#configuration.contacts.linkedin.displayText] \
        GitHub: #link(configuration.contacts.github.url)[#configuration.contacts.github.displayText] \
      ]
      #line(length: 100%)
    ]

    = Summary

    #par[
      #set text(
        eval(configuration.font.size.education_description),
        font: configuration.font.minor_highlight,
      )
      Software developer with experience of *infrastructure* and *DevOps*. Most familiar with C\# and Rust, but also work with common languages.
    ]

    = Education

    #{
      for place in configuration.education [
        #par[
          #set text(
            size: eval(configuration.font.size.heading),
            font: configuration.font.general,
          )
          #place.to \
          #link(place.university.link)[#place.university.name]
        ]
        #par[
          #set text(
            eval(configuration.font.size.education_description),
            font: configuration.font.minor_highlight,
          )
          #place.degree #place.major \
          #place.track track
        ]
      ]
    }

    = Skills

    #{
      for skill in configuration.skills [
        #par[
          #set text(size: eval(configuration.font.size.description))
          #set text(
            // size: eval(configuration.font.size.tags),
            font: configuration.font.minor_highlight,
          )
          *#skill.name*
          #{
            for skillitem in skill.items [
              #linebreak()
              #skillitem.join(" • ")
            ]
          }
        ]
      ]
    }
  ]
}

#let mainSection = {
  [

    // #par[
    //   #set align(center)
    //   #figure(
    //     image("images/Kodak 20 Zanvoort Lumi.jpg", width: 6em),
    //     placement: top,
    //   )
    // ]

    #par[
      #set text(
        size: eval(configuration.font.size.heading_huge),
        font: configuration.font.general,
      )
      *#configuration.contacts.name*
    ]

    #par[
      #set text(
        size: eval(configuration.font.size.heading),
        font: configuration.font.minor_highlight,
        top-edge: 0pt,
      )
      #configuration.contacts.title
    ]

    = Experience

    #{
      for job in configuration.jobs [
        #par(justify: false)[
          #set text(
            size: eval(configuration.font.size.heading),
            font: configuration.font.general,
          )
          #job.from – #job.to \
          *#job.position*
          #{
            for jc in job.company [
              #link(jc.link)[\@ #jc.name]
            ]
          }
        ]
        #par(justify: false, leading: eval(configuration.paragraph.leading))[
          #set text(
            size: eval(configuration.font.size.description),
            font: configuration.font.general,
          )
          #{
            for point in job.description [
              #h(0.5cm) • #point \
            ]
          }
        ]
        #par(justify: true, leading: eval(configuration.paragraph.leading))[
          #set text(
            size: eval(configuration.font.size.tags),
            font: configuration.font.minor_highlight,
          )
          #{
            let tag_line = job.tags.join(" • ")
            tag_line
          }
        ]
      ]
    }
  ]
}

#{
  grid(
    columns: (2fr, 5fr),
    column-gutter: 2em,
    sidebarSection, mainSection,
  )
}
