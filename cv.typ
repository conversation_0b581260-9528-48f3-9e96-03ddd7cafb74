#import "@preview/basic-resume:0.2.8": *

#let dummy = false
#let name = "<PERSON>"
#let phone = "(*************"
#let email = "<EMAIL>"
#{
  if not dummy {
    name = "<PERSON><PERSON>"
    phone = "(*************"
    email = "<EMAIL>"
  }
}

#let rn = context name.get()
#show: resume.with(
  author: name,
  location: "Surrey, B.C.",
  email: email,
  github: "sgkoishi",
  phone: phone,
  accent-color: "#26428b",
  font: "Arial",
  paper: "us-letter",
  font-size: 13pt,
  author-position: left,
  personal-info-position: left,
)

== Education

#edu(
  institution: "British Columbia Institute of Technology",
  location: [Burnaby, BC],
  dates: [2023],
  degree: [Bachelor of Technology, Computer System (Network Security and Development)],
)

== Projects

#project(
  name: "Localizer",
  role: "Core Developer",
  dates: dates-helper(start-date: "Jan. 2019", end-date: "Apr. 2021"),
  url: "github.com/chi-rei-den/Localizer",
)
- Developed a localization tool based on C\# assembly (CIL bytecode) modification.
- Extracts resources through reflection and static bytecode analysing, loads localized resources with both emitting and runtime hot patching.
- Built-in immediate mode UI controls the core function without any requirement of programming experience.

#project(
  name: [TShock, Terraria server toolkit],
  role: [Maintainer],
  dates: dates-helper(end-date: "Present"),
  url: "github.com/Pryaxis/TShock/",
)
- The most popular third-party Terraria Server that supports vanilla games.
- Featuring player management and plugin system.
- Manage bug fixes, PR reviews

#project(
  name: [Terraria Mod Browser],
  role: [Core Developer],
  dates: dates-helper(start-date: "Dec. 2019", end-date: "Jan. 2023"),
)
- Developed a website providing game mods and localization listing, sharing and downloading.
- The second largest mod repository, with daily active users over 30,000 and handles over 400GB bandwidth at the peak day.
- Using ASP.NET Core Razor, later rewritten with Rust and vanilla JS.

#project(
  name: [Fan-made Anti-Cheating Game Extention],
  role: [Core Developer],
  dates: dates-helper(start-date: "2016", end-date: "2019"),
)
- Developed an extension to prevent client-side modification and illegal memory editing, without access to the game's source code.
- Stopped the use of common cheating tools that uses memory scanning like Cheat Engine.

#project(
  name: [Chatbot Integration Framework],
  role: [Maintainer],
  dates: dates-helper(start-date: "Aug. 2018", end-date: "2020"),
)
- A third party chatbot integration framework for QQ, one of the most popular IM software in China, before they released their public API.
- Receives more than 200 stars and 80 forks within a short time. Later close-sourced for business and copyright reason, but public forks available beore it turns private.

== Skills
- *Programming Languages*: C\#, Rust, Shell, Python, TypeScript, Java, Assembly
- *Tools and Technologies*: Git, AWS, MongoDB, MySQL, PostgreSQL, Azure, Ansible
