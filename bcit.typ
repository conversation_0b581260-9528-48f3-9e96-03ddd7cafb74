#let title = "TitlePageTOC"
#let authors = (
    (name: "<PERSON><PERSON>", email: "<EMAIL>"),
)
#let logo = none
#let date = "March 28, 2023"

#set document(author: authors.map(a => a.name), title: title)
#set page(numbering: "1", number-align: center)
#set text(font: "Linux Libertine", lang: "en")
#v(0.6fr)
#if logo != none {
  align(right, image(logo, width: 26%))
}
#v(8fr)

#text(1.1em, date)
#v(1.2em, weak: true)
#text(2em, weight: 700, title)

#pad(
  top: 0.7em,
  right: 20%,
  grid(
    columns: (1fr,) * calc.min(3, authors.len()),
    gutter: 1em,
    ..authors.map(author => align(start)[
      *#author.name* \
      #author.email
    ]),
  ),
)

#v(4fr)

#outline(depth: 3, indent: true)
#pagebreak()

#set par(justify: true)

= Introduction
#lorem(60)
Glaciers as the one shown in
@glaciers will cease to exist if
we don't take action soon!

#figure(
  image("test/glacier.png", width: 80%),
  caption: [
    _Glaciers_ form an important part
    of the earth's climate system.
  ],
) <glaciers>

#lorem(60)

== In this paper
#lorem(20)