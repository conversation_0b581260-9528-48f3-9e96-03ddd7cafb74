[contacts]
name = "<PERSON><PERSON>"
title = "Software Developer"
email = "<EMAIL>"
phone = "************"

[contacts.linkedin]
url = "https://www.linkedin.com/in/yonghan-r-225b88167/"
displayText = "yonghan-r"

[contacts.github]
url = "https://github.com/sgkoishi/"
displayText = "sgkoishi"

[[skills]]
name = "Proficient"
items = [ ["C#", "Rust", "Shell"], ["TypeScript / JavaScript"], ["Git", "AWS", "MongoDB"], ["MySQL / MariaDB"] ]

[[skills]]
name = "Familiar"
items = [ ["Azure", "Java", "PostgreSQL"], ["Ansible", "Assembly"] ]

[[jobs]]
position = "Localizer"
description = [
  "Developed a localization tool based on C# assembly (CIL bytecode) modification.",
  "It extracts resources through reflection and static bytecode analysing, loads localized resources with both emitting and runtime hot patching.",
  "Modular design and inverse of control allows easy updating or replacement of individual module.",
  "Built-in immediate mode UI controls the core function without any requirement of programming experience.",
]
from = "Jan. 2019"
to = "Apr. 2021"
tags = [ "Byte-code Manipulation", "Open Source" ]
company = []

[[jobs]]
position = "Terraria Mod Browser"
description = [
  "Developed a website providing game mods and localization listing, sharing and downloading.",
  "The modding API framework is available on Steam, while this website is the second largest mod repository.",
  "Daily active users over 30,000 and handles over 400GB bandwidth at the peak day.",
  "Using ASP.NET Core, later rewritten with Rust and vanilla JS.",

]
from = "Dec. 2019"
to = "Jan. 2023"
tags = [ "Backend Development", "Cloud Services", "Cost Control" ]
company = [{name = "OpenSource", link="https://github.com/chi-rei-den/Localizer/"}]

[[jobs]]
position = "Fan-made Anti-Cheating Game Extention"
description = [
  "Developed an extension to prevent client-side modification and illegal memory editing.",
  "Stopped the use of common cheating tools that uses memory scanning like Cheat Engine.",
]
from = "2016"
to = "2019"
tags = [ "Memory Protection", "Obfuscation" ]
company = []

[[jobs]]
position = "Maintainer of Chatbot Integration Framework"
description = [
  "A third party chatbot integration framework for QQ, one of the most popular IM software in China.",
  "Follows the cleanroom design and inversion of control.",
  "Written in C# .NET Framework, with a cross-platform branch using .NET Core.",
  "Receives more than 200 stars and 80 forks. Close-sourced right now, but public forks available beore private."
]
from = "Aug. 2018"
to = "2020"
tags = []
company = []

[[education]]
degree = "B.Tech."
major = "Computer System Technology"
track = "Network Security and Development"
to = "2023"

  [education.university]
  name = "British Columbia Institute of Technology"
  link = "https://www.bcit.ca/"

[font]
general = "Source Sans Pro"
minor_highlight = "Source Sans Pro"

[font.size]
heading_huge = "20pt"
heading_large = "18pt"
heading = "14pt"
description = "10pt"
tags = "10pt"
education_description = "10pt"
contacts = "10pt"

[paragraph]
leading = "4pt"
