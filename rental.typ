#set text(font: "<PERSON><PERSON><PERSON>")
#set page("us-letter", margin: (x: 50pt, y: 50pt))
#align(center, text(18pt)[*RENTAL APPLICATION*])

#show grid: set block(spacing: 0em)
#set grid(inset: (x: 3pt, y: 7pt), stroke: (x, y) => if calc.odd(x) {
  (bottom: 0.7pt + black)
}, gutter: 0em)
#grid(
  columns: 1,
  text(
    11pt,
  )[All other applicants over the age of 18 must complete and sign their own
    applications. #linebreak() Each person 18 years or older must be on the lease
    and have their credit/criminal background checked],
)

#text(
  14pt,
  weight: "bold",
)[
  #grid(
    columns: (23%, 47%, 18%, 12%),
    stroke: (x, y) => if (y == 0 and x == 1) or (y == 1 and x == 1) or (y == 1 and x == 3) {
      (bottom: 0.7pt + black)
    },
    [Property Address:],
    [Room-10220 Semiahmoo Road],
    [],
    [],
    [],
    [Surrey BC V3T 3N4],
    [Monthly Rent:],
    [\$ 980.-],
  )
]

#set text(12pt)
#grid(
  columns: (22%, 28%, 22%, 28%),
  [*Primary Applicant*],
  [<PERSON><PERSON>],
  [*Co-applicant*],
  [ N/A ],
  [Phone number],
  [ (************* ],
  [Phone number],
  [ N/A ],
  [Email],
  [ i\@sgkoi.dev ],
  [Email],
  [ N/A ],
  [Birthdate],
  [ 1999-Jul-04 ],
  [Birthdate],
  [ N/A ],
  [Social Insurance \#],
  [ 938463908 ],
  [Social Insurance \#],
  [ N/A ],
  [Driver’s Licence \#],
  [ N/A ],
  [Driver’s Licence \#],
  [ N/A ],
)
#grid(
  columns: (6%, 10%, 6%, 28%, 6%, 10%, 6%, 28%),
  [Prov.],
  [ N/A ],
  [Exp.],
  [ N/A ],
  [Prov.],
  [ N/A ],
  [Exp.],
  [ N/A ],
)
#grid(
  columns: (19%, 47%, 6%, 14%, 6%, 8%),
  [*Current Address*],
  [ 16127 87 Ave ],
  [City],
  [ Surrey ],
  [Prov.],
  [ B.C. ],
)
#grid(
  columns: (27%, 43%, 10%, 20%),
  [Current Landlord's Name],
  [ Gary (left Canada) ],
  [Phone \#],
  [ (************* ],
)
#grid(
  columns: (66%, 1fr),
  [How long have you stayed at this address and Reason for leaving:],
  [ One and a half year. ],
)
#grid(
  columns: (5pt, 100% - 5pt),
  [],
  [ The owner is moving back to China, house was sold. ],
)
#grid(
  columns: (19%, 47%, 6%, 14%, 6%, 8%),
  [*Previous Address*],
  [ 5645 Barker Ave ],
  [City],
  [ Burnaby ],
  [Prov.],
  [ B.C. ],
)
#grid(
  columns: (27%, 43%, 10%, 20%),
  [Previous Landlord's Name],
  [ N/A ],
  [Phone \#],
  [ N/A ],
)
#grid(
  columns: (66%, 1fr),
  [How long have you stayed at this address and Reason for leaving:],
  [ > 4 years of university time ],
)
#grid(
  columns: (5pt, 100% - 5pt),
  [],
  [ My grandparents' apartment, later provided to a relative. ],
)
#grid(
  columns: (20%, 30%, 10%, 16%, 10%, 14%),
  [*Present Employer*],
  [ N/A ],
  [Position],
  [ N/A ],
  [Phone \#],
  [ N/A ],
)
#grid(
  columns: (20%, 30%, 10%, 16%),
  [Manager's name],
  [ N/A ],
  [Phone \#],
  [ N/A ],
)
#grid(columns: (22%, 1fr), [Employer's Address], [ N/A ])
#grid(columns: (36%, 14%), [How long have you been at this job], [ N/A ])
#grid(
  columns: (18%, 22%, 23%, 37%),
  [Monthly Income],
  [ N/A ],
  [Other Income/source],
  [ Family support ],
)

#v(1em)
#place(bottom + center, image("rental_ad.jpg"))

#pagebreak()

#show grid: set block(spacing: 5pt)
#grid(
  columns: (12%, 15%, 8%, 15%, 6%, 6%, 22%, 16%),
  [*Car:* Make],
  [ N/A ],
  [Model],
  [ N/A ],
  [Year],
  [ N/A ],
  [Prov./License plate \#],
  [ N/A ],
)
#grid(
  columns: (37%, 30%, 10%, 23%),
  [*Nearest Relative NOT* living with you],
  [ My parents in China ],
  [Phone \#],
  [ (I guess not needed?) ],
)
#grid(
  columns: (14%, 20%, 12%, 54%),
  [Relationship],
  [],
  [Address],
  [ Outside canada ],
)
#grid(
  columns: 1,
  [Have you ever been evicted? (Please circle) Yes No #place(right, dx: 4pt, dy: -13pt, circle(radius: 10pt))],
)

#grid(
  columns: (24%, 8%, 60%, 8%),
  [*Total number of adults*],
  [ 1 ],
  [*Total number of children* living with you under the age of 18],
  [ N/A ],
)
#grid(
  columns: (42%, 1fr),
  [Name and relation of all other occupants],
  [ N/A ],
)
#grid(columns: (5pt, 100% - 5pt), [], [ N/A ])

#v(2em)
#grid(
  columns: 1,
  [I (We) CERTIFY that answers given herein are true and complete to the best of my
    (our) knowledge. I #linebreak() (we) authorize investigation of all statements
    contained in this application for tenant screening as may #linebreak() be
    necessary in arriving at a tenant decision including credit check. I (we)
    understand that the landlord #linebreak() may terminate the rental agreement
    entered into for any misrepresentation made about. I (We) also #linebreak() authorize
    Landlord to run credit checks on me (us) in the future at landlord’s own expense
    in his #linebreak() efforts to collect any money owed by me (us) to Landlord.],
)
#v(2em)

#grid(
  columns: (10%, 60%, 6%, 24%),
  [Signature],
  [Yonghan Ruan],
  [Date],
  [2025-May-21],
)
#v(2em)
#grid(columns: (10%, 60%, 6%, 24%), [Signature], [ N/A ], [Date], [ N/A ])
#v(8pt)
#grid(columns: 1, [
  *Minimum two references:*
])
#grid(columns: (36%, 1fr), [Name, relationship, and contact info], [])
#grid(columns: (5pt, 100% - 5pt), [], [\ ])
#grid(columns: (36%, 1fr), [Name, relationship, and contact info], [])
#grid(columns: (5pt, 100% - 5pt), [], [\ ])
#place(bottom + center, image("rental_ad.jpg"))

#pagebreak()

#image("passport2.jpg")

#pagebreak()

#image("pgwp2.jpg")
