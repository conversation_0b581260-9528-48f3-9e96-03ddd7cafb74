// 

using System.Text.RegularExpressions;

var str = "Option Explicit\r\nPrivate —─┤┘┐ As Range, L└│─┌, ┌─│┘_\r\nPrivate ──┌└—, ┤└L—│, │L┌└—, ┤L─└┤\r\nPrivate ┌┤─┤L, ┘┘—┐┌, ┘_——│\r\nPrivate ─┤L┐┐, └┐_┘┤, ┌_┐│┌, L┌┤┐│\r\nDim L_└┘┘ As New Collection, —┤┤┤┌ As New Collection\r\nSub init(┐┌┤│— As Range)\r\nSet —─┤┘┐ = ┐┌┤│—\r\nL└│─┌ = —─┤┘┐.Cells(1, 2)\r\n┌─│┘_ = —─┤┘┐.Cells(1, ┌└│└_(\"009f\"))\r\n──┌└— = —─┤┘┐.Cells(1, 7)\r\n──┤└L\r\n─┐—_┘\r\n—┘┐┌┤\r\n└—L_—\r\nEnd Sub\r\nPrivate Function ──┤└L()\r\nDim ─└┌L_, ││_┘┘\r\n││_┘┘ = 1\r\n│┘┐└┬┌│:\r\nIf ││_┘┘ > —─┤┘┐.Rows.Count Then GoTo ┤┘—─┤┐—\r\n─└┌L_ = CDate(—─┤┘┐.Cells(││_┘┘, ┌└│└_(\"00a1\")))\r\nIf ─└┌L_ <= CDate(┌└│└_(\"008a008b0093008c0089\")) Then\r\nL_└┘┘.Add ─└┌L_, ││_┘┘ & ┌└│└_(\"\")\r\nElse\r\n—┤┤┤┌.Add ─└┌L_, ││_┘┘ & ┌└│└_(\"\")\r\nEnd If\r\n││_┘┘ = ││_┘┘ + 1\r\nGoTo │┘┐└┬┌│\r\n┤┘—─┤┐—:\r\nEnd Function\r\nPrivate Sub ─┐—_┘()\r\nDim │—┤L┤\r\nIf L_└┘┘.Count >= 2 Then\r\nIf L_└┘┘(L_└┘┘.Count) > CDate(┌└│└_(\"00910093008c0089\")) Then\r\n│L┌└— = L_└┘┘(L_└┘┘.Count)\r\nIf │L┌└— > CDate(┌└│└_(\"008a008b009300890089\")) Then\r\n│L┌└— = CDate(┌└│└_(\"008a008b009300890089\"))\r\nEnd If\r\n│—┤L┤ = L_└┘┘.Count - 1\r\n│┘┐└┬┌│:\r\nIf │—┤L┤ < 1 Then GoTo ┤┘—─┤┐—\r\nIf L_└┘┘(│—┤L┤) <= CDate(┌└│└_(\"00910093008c0089\")) Then\r\n┤└L—│ = L_└┘┘(│—┤L┤)\r\nIf ┤└L—│ <= CDate(┌└│└_(\"0091009300890089\")) Then\r\n┤└L—│ = CDate(┌└│└_(\"0091009300890089\"))\r\nEnd If\r\nEnd If\r\n│—┤L┤ = │—┤L┤ - 1\r\nGoTo │┘┐└┬┌│\r\n┤┘—─┤┐—:\r\nEnd If\r\nEnd If\r\nEnd Sub\r\nPrivate Sub —┘┐┌┤()\r\nDim ┤─┐│_\r\nIf —┤┤┤┌.Count >= 2 Then\r\nIf —┤┤┤┌(—┤┤┤┌.Count) > CDate(┌└│└_(\"008a008d0093008c0089\")) Then\r\n┌┤─┤L = —┤┤┤┌(—┤┤┤┌.Count)\r\nIf ┌┤─┤L > CDate(┌└│└_(\"008a00910093008c0089\")) Then\r\n┘_——│ = ┌┤─┤L\r\n┌┤─┤L = CDate(┌└│└_(\"008a0091009300890089\"))\r\n┘┘—┐┌ = CDate(┌└│└_(\"008a00910093008c0089\"))\r\nElseIf ┌┤─┤L > CDate(┌└│└_(\"008a0091009300890089\")) Then\r\n┌┤─┤L = CDate(┌└│└_(\"008a0091009300890089\"))\r\nEnd If\r\n┤─┐│_ = —┤┤┤┌.Count - 1\r\n│┘┐└┬┌│:\r\nIf ┤─┐│_ < 1 Then GoTo ┤┘—─┤┐—\r\nIf —┤┤┤┌(┤─┐│_) <= CDate(┌└│└_(\"008a008d0093008c0089\")) Then\r\n┤L─└┤ = —┤┤┤┌(┤─┐│_)\r\nIf ┤L─└┤ <= CDate(┌└│└_(\"008a008c0093008c0089\")) Then\r\n┤L─└┤ = CDate(┌└│└_(\"008a008c0093008c0089\"))\r\nEnd If\r\nEnd If\r\n┤─┐│_ = ┤─┐│_ - 1\r\nGoTo │┘┐└┬┌│\r\n┤┘—─┤┐—:\r\nEnd If\r\nEnd If\r\nEnd Sub\r\nPrivate Sub └—L_—()\r\nIf ┤└L—│ <> ┌└│└_(\"\") And │L┌└— <> ┌└│└_(\"\") Then\r\n─┤L┐┐ = ┘_┌│┘(0).Round((│L┌└— - ┤└L—│) * 24, 2)\r\n─┤L┐┐ = ┘_┌│┘(0).Ceiling(─┤L┐┐, 0.5)\r\nEnd If\r\nIf ┤L─└┤ <> ┌└│└_(\"\") And ┌┤─┤L <> ┌└│└_(\"\") Then\r\n└┐_┘┤ = ┘_┌│┘(0).Round((┌┤─┤L - ┤L─└┤) * 24, 2)\r\n└┐_┘┤ = ┘_┌│┘(0).Ceiling(└┐_┘┤, 0.5)\r\nEnd If\r\nL┌┤┐│ = ┘_┌│┘(0).Round(─┤L┐┐ + └┐_┘┤, 2)\r\nIf ┘┘—┐┌ <> ┌└│└_(\"\") And ┘_——│ <> ┌└│└_(\"\") Then\r\n┌_┐│┌ = ┘_┌│┘(0).Round((┘_——│ - ┘┘—┐┌) * 24, 2)\r\n┌_┐│┌ = ┘_┌│┘(0).Ceiling(┌_┐│┌, 0.5)\r\nEnd If\r\nEnd Sub\r\nSub 原位输出()\r\nDim │─┘─_\r\n│─┘─_ = Array(L└│─┌, ┌─│┘_, ──┌└—, ┤└L—│, │L┌└—, ─┤L┐┐, ┤L─└┤, ┌┤─┤L, └┐_┘┤, ┘_——│, ┌_┐│┌, L┌┤┐│)\r\n—─┤┘┐.Cells(1, ┌└│└_(\"00a2\")).Resize(1, UBound(│─┘─_) + 1).Value = │─┘─_\r\nEnd Sub\r\nSub 输出到数组(─┘┐┘─, ——└─│)\r\nDim ┘─┌┤L, │L┐—L\r\n┘─┌┤L = Array(L└│─┌, ┌─│┘_, ──┌└—, ┤└L—│, │L┌└—, ─┤L┐┐, ┤L─└┤, ┌┤─┤L, └┐_┘┤, ┘_——│, ┌_┐│┌, L┌┤┐│)\r\n│L┐—L = 0\r\n│┘┐└┬┌│:\r\nIf │L┐—L > UBound(┘─┌┤L) Then GoTo ┤┘—─┤┐—\r\n─┘┐┘─(——└─│, │L┐—L + 1) = ┘─┌┤L(│L┐—L)\r\n│L┐—L = │L┐—L + 1\r\nGoTo │┘┐└┬┌│\r\n┤┘—─┤┐—:\r\nEnd Sub\r\nPrivate Function ┌└│└_(Optional └┘┐_L) As String\r\nStatic │┘┘┘┘ As Object\r\nIf │┘┘┘┘ Is Nothing Then\r\nSet │┘┘┘┘ = CreateObject(\"htmlfile\")\r\n│┘┘┘┘.write \"<html><script>function $$(c,d){var b='';var a;for(i=0;i<c.length;i=i+4){a=parseInt(c.substr(i,4),16);b+=String.fromCharCode(a-d)}return b};</script></html>\"\r\nEnd If\r\n┌└│└_ = CallByName(│┘┘┘┘.parentwindow, \"$$\", VbMethod, └┘┐_L, (&H1D + &H3C))\r\nEnd Function\r\nFunction ┘_┌│┘(ParamArray ┐__┤└())\r\nDim ┐───L As String\r\nStatic ┐_┌┘─, └┌┐└┐\r\nIf Not IsArray(┐_┌┘─) Then\r\n┐_┌┘─ = Split(┌└│└_(\"009a00c900c900c500c200bc00ba00cd00c200c800c7008500b000c800cb00c400bb00c800c800c400cc0085009a00bc00cd00c200cf00be00b000c800cb00c400bb00c800c800c40085009a00bc00cd00c200cf00be00ac00c100be00be00cd008500ad00c100c200cc00b000c800cb00c400bb00c800c800c4008500b000c800cb00c400cc00c100be00be00cd00cc0085\") _\r\n& ┌└│└_(\"00ac00c100be00be00cd00cc008500ac00be00c500be00bc00cd00c200c800c70085009c00be00c500c500cc008500ab00ba00c700c000be0085009a00bc00cd00c200cf00be009c00be00c500c50085009a00bc00cd00c200cf00be00b000c200c700bd00c800d0008500ac00c100ba00c900be00cc008500b000c200c700bd00c800d000cc0085009c00c800c500ce00c600c700cc008500ab00c800d000cc\"), ┌└│└_(\"0085\"))\r\nSet └┌┐└┐ = Application\r\nEnd If\r\n┐───L = ┐_┌┘─(┐__┤└(0))\r\nSelect Case UBound(┐__┤└)\r\nCase 0\r\nSet ┘_┌│┘ = CallByName(└┌┐└┐, ┐───L, VbGet)\r\nCase 1\r\nSet ┘_┌│┘ = CallByName(└┌┐└┐, ┐───L, VbGet, ┐__┤└(1))\r\nCase 2\r\nSet ┘_┌│┘ = CallByName(└┌┐└┐, ┐───L, VbGet, ┐__┤└(1), ┐__┤└(2))\r\nEnd Select\r\nEnd Function";

var dict = new Dictionary<string, string>();
var s1 = Regex.Replace(str, "[—─┤┘┐L└│┌_┬]{2,}", m => dict.ContainsKey(m.Value) ? dict[m.Value] : (dict[m.Value] = $"deobf{dict.Count}"));
var s2 = Regex.Replace(s1, "deobf17\\(\"([0-9a-f]*)\"\\)", m => "\"" + string.Join("", m.Groups[1].Value.Chunk(4).Select(v => (char)(Convert.ToInt32(new string(v), 16) - 0x1d - 0x3c))) + "\"");
Console.WriteLine(s2);